"""Set of constants."""

DEFAULT_TEMPERATURE = 0.1
DEFAULT_CONTEXT_WINDOW = 3900  # tokens
DEFAULT_NUM_OUTPUTS = 256  # tokens
DEFAULT_NUM_INPUT_FILES = 10  # files

DEFAULT_EMBED_BATCH_SIZE = 10

DEFAULT_CHUNK_SIZE = 1024  # tokens
DEFAULT_CHUNK_OVERLAP = 20  # tokens
DEFAULT_SIMILARITY_TOP_K = 2
DEFAULT_IMAGE_SIMILARITY_TOP_K = 2

# NOTE: for text-embedding-ada-002
DEFAULT_EMBEDDING_DIM = 1536

# context window size for llm predictor
COHERE_CONTEXT_WINDOW = 2048
AI21_J2_CONTEXT_WINDOW = 8192


TYPE_KEY = "__type__"
DATA_KEY = "__data__"
VECTOR_STORE_KEY = "vector_store"
IMAGE_STORE_KEY = "image_store"
GRAPH_STORE_KEY = "graph_store"
INDEX_STORE_KEY = "index_store"
DOC_STORE_KEY = "doc_store"
PG_STORE_KEY = "property_graph_store"

# llama-cloud constants
DEFAULT_PIPELINE_NAME = "default"
DEFAULT_PROJECT_NAME = "Default"
DEFAULT_BASE_URL = "https://api.cloud.llamaindex.ai"
DEFAULT_APP_URL = "https://cloud.llamaindex.ai"
