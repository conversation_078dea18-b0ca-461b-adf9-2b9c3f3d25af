/* 全局样式 */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --sidebar-width: 280px;
  --navbar-height: 56px;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding-top: var(--navbar-height);
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  top: var(--navbar-height);
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
  width: var(--sidebar-width);
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin: 0.125rem 0.5rem;
  transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
  color: var(--primary-color);
  background-color: rgba(13, 110, 253, 0.1);
}

.sidebar .nav-link.active {
  color: var(--primary-color);
  background-color: rgba(13, 110, 253, 0.15);
  font-weight: 600;
}

.sidebar .nav-link i {
  margin-right: 0.5rem;
  width: 16px;
  text-align: center;
}

.sidebar-heading {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

/* 主内容区域 */
main {
  margin-left: var(--sidebar-width);
  min-height: calc(100vh - var(--navbar-height));
}

/* 页面内容 */
.page-content {
  display: none;
}

.page-content.active {
  display: block;
}

/* 卡片样式 */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: rgba(13, 110, 253, 0.05);
  border-bottom: 1px solid rgba(13, 110, 253, 0.1);
  font-weight: 600;
}

/* 文件上传区域 */
.file-upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 0.5rem;
  padding: 3rem 2rem;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
  border-color: var(--primary-color);
  background-color: rgba(13, 110, 253, 0.1);
  transform: scale(1.02);
}

.file-upload-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

/* 进度条样式 */
.progress {
  height: 1rem;
  border-radius: 0.5rem;
}

.progress-bar {
  border-radius: 0.5rem;
  transition: width 0.6s ease;
}

/* 状态徽章 */
.status-badge {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 500;
}

.status-processing {
  background-color: rgba(255, 193, 7, 0.1);
  color: #856404;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-completed {
  background-color: rgba(25, 135, 84, 0.1);
  color: #0f5132;
  border: 1px solid rgba(25, 135, 84, 0.3);
}

.status-failed {
  background-color: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

/* 新的聊天界面样式 */
.chat-messages-container {
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
}

.chat-message-new {
  margin-bottom: 1.5rem;
  max-width: 85%;
  animation: fadeInUp 0.3s ease-out;
}

.chat-message-new.user {
  margin-left: auto;
  margin-right: 0;
}

.chat-message-new.assistant {
  margin-left: 0;
  margin-right: auto;
}

.chat-message-new .message-content {
  padding: 1rem 1.25rem;
  border-radius: 1.25rem;
  position: relative;
  word-wrap: break-word;
  line-height: 1.5;
}

.chat-message-new.user .message-content {
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  border-bottom-right-radius: 0.5rem;
}

.chat-message-new.assistant .message-content {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-message-new.loading .message-content {
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
  animation: pulse 1.5s infinite;
}

.message-timestamp {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.5rem;
  text-align: right;
}

.chat-message-new.user .message-timestamp {
  text-align: right;
}

.chat-message-new.assistant .message-timestamp {
  text-align: left;
}

.chat-meta-info {
  text-align: center;
  font-size: 0.875rem;
  color: #6c757d;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(108, 117, 125, 0.1);
  border-radius: 1rem;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.chat-system-info {
  text-align: center;
  font-size: 0.875rem;
  color: var(--info-color);
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(13, 202, 240, 0.1);
  border-radius: 1rem;
  border: 1px solid rgba(13, 202, 240, 0.2);
}

.chat-input-area {
  background-color: white;
  border-radius: 0 0 0.5rem 0.5rem;
}

.welcome-message {
  padding: 2rem;
  text-align: center;
}

/* 来源信息卡片样式 */
.source-card-new {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid var(--primary-color);
  transition: all 0.2s ease;
}

.source-card-new:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.source-title {
  color: var(--primary-color);
  font-weight: 600;
}

.source-meta {
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 0.5rem;
}

.source-content {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 旧的来源信息卡片样式 - 保留兼容性 */
.source-card {
  border-left: 4px solid var(--primary-color);
  background-color: rgba(13, 110, 253, 0.05);
  margin-bottom: 0.5rem;
}

.source-score {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
}

/* 旧的聊天界面样式 - 保留兼容性 */
.chat-container {
  height: 500px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: white;
}

.chat-message {
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  max-width: 80%;
}

.chat-message.user {
  background-color: var(--primary-color);
  color: white;
  margin-left: auto;
  text-align: right;
}

.chat-message.assistant {
  background-color: #e9ecef;
  color: #333;
  margin-right: auto;
}

.chat-input-container {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
  margin-top: 1rem;
}

/* 表格样式 */
.table {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
  background-color: rgba(13, 110, 253, 0.1);
  border-bottom: 2px solid rgba(13, 110, 253, 0.2);
  font-weight: 600;
  color: #333;
}

/* 按钮样式 */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.form-control,
.form-select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: static;
    width: 100%;
    height: auto;
    padding: 0;
  }

  main {
    margin-left: 0;
  }

  .navbar-brand {
    font-size: 1rem;
  }
}

/* 暗色主题 */
[data-theme="dark"] {
  --light-color: #212529;
  --dark-color: #f8f9fa;
}

[data-theme="dark"] body {
  background-color: #212529;
  color: #f8f9fa;
}

[data-theme="dark"] .sidebar {
  background-color: #343a40 !important;
}

[data-theme="dark"] .sidebar .nav-link {
  color: #f8f9fa;
}

[data-theme="dark"] .card {
  background-color: #343a40;
  color: #f8f9fa;
}

[data-theme="dark"] .table {
  background-color: #343a40;
  color: #f8f9fa;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: #495057;
  border-color: #6c757d;
  color: #f8f9fa;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
