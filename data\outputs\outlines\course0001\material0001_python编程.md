# 函数基础与应用

## 函数概述
### 函数通过调用实现任务复用，避免重复编写相同代码。
### 使用函数让程序编写、阅读、测试和修复都更加容易。
### 可以通过多种方式向函数传递信息以满足不同需求。
### 函数可以用于显示信息或处理数据并返回值。
### 函数可存储在模块中，主程序更整洁且易于维护。

## 函数定义与调用
### 函数定义使用def关键字，指定函数名和参数列表，以冒号结尾。
### 函数体由缩进部分组成，可包含文档字符串描述功能。
### 调用函数时需使用函数名及括号，并传递必要的实参。
### 形参是定义时指定的信息占位符，实参是调用时提供的具体数据。
### 可以多次调用同一个函数，实参不同输出不同结果。

## 实参传递方式
### 位置实参要求实参顺序与形参顺序一致，顺序错误会导致意外结果。
### 关键字实参通过指定参数名和值，避免顺序混淆，提升可读性。
### 默认值允许部分参数在调用时可省略，简化常见调用场景。
### 混合使用位置实参、关键字实参和默认值可实现多种调用方式。
### 实参与形参数量不匹配会导致错误，需保持一致性。

## 函数返回值
### return语句用于将处理结果返回到调用处，实现数据流转。
### 可以返回简单的值，如字符串、数字等。
### 通过默认值和条件判断，实参可以设置为可选，实现灵活调用。
### 函数可返回字典等复杂数据结构，便于进一步处理和扩展。
### 函数与while循环等结构结合，可实现交互式、持续获取输入的程序。

## 列表与函数
### 向函数传递列表可以批量处理元素，提高处理效率。
### 函数内部可直接修改传入的列表，实现数据状态转移。
### 通过切片传递列表副本可避免对原始列表的修改，确保数据安全。
### 将相关操作封装为函数有助于主程序结构清晰，便于扩展和维护。
### 每个函数应只负责一项具体工作，复杂任务宜拆分成多个函数。

## 可变参数
### 使用*args可收集任意数量的位置实参，作为元组处理。
### *args参数需放在函数参数列表最后，支持混合位置实参。
### **kwargs可收集任意数量的关键字实参，作为字典处理。
### 函数可同时使用固定参数、*args和**kwargs，实现高度灵活的参数接收。
### 正确命名和顺序安排参数，有助于函数调用的正确性和可读性。

## 模块与导入
### 模块是.py文件，包含一组相关函数，可通过import导入使用。
### import module_name后需使用module_name.function_name()调用函数。
### 可通过from module_name import function_name直接导入特定函数，无需模块名前缀。
### as关键字可为函数或模块指定别名，避免命名冲突或简化调用。
### from module_name import *可导入所有函数，但易引发命名冲突，通常不推荐。
### 导入方法选择应以代码清晰和可维护性为主。

## 编码规范
### 函数名应简洁且具描述性，使用小写字母和下划线。
### 每个函数应包含文档字符串，说明功能和使用方法。
### 形参默认值赋值时等号两侧不加空格，函数调用也遵循此规范。
### 每行建议不超过79字符，参数多时可分行并缩进对齐。
### 多个函数间应以两个空行分隔，import语句放文件开头。
### 规范的函数和模块命名有助于代码维护和团队协作。

## 函数优势
### 函数使得编写一次代码后可多次复用，有助于减少代码量。
### 通过修改函数体可全局影响所有调用，提高维护效率。
### 良好的函数名和结构便于理解程序整体逻辑。
### 分工明确的函数便于单元测试和调试，提升代码质量。
### 遵循函数相关指南可让程序结构良好，便于自己和他人阅读与修改。
### 函数是实现高效、可维护和可扩展程序的基础工具。