<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID冲突修复测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-info">
            <h4><i class="bi bi-bug"></i> ID冲突修复测试</h4>
            <p class="mb-0">测试course_material_id参数是否能正确发送</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>大纲生成页面的输入框</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="course-id" class="form-label">课程ID</label>
                            <input type="text" class="form-control" id="course-id" value="outline_course">
                        </div>
                        <div class="mb-3">
                            <label for="course-material-id" class="form-label">课程材料ID</label>
                            <input type="text" class="form-control" id="course-material-id" value="outline_material">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>RAG聊天页面的输入框</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="rag-course-id" class="form-label">RAG课程ID</label>
                            <input type="text" class="form-control" id="rag-course-id" value="rag_course">
                        </div>
                        <div class="mb-3">
                            <label for="rag-course-material-id" class="form-label">RAG课程材料ID</label>
                            <input type="text" class="form-control" id="rag-course-material-id" value="rag_material">
                        </div>
                        <div class="mb-3">
                            <label for="rag-collection-name" class="form-label">RAG集合名称</label>
                            <input type="text" class="form-control" id="rag-collection-name" value="rag_collection">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testIdConflict()">测试ID冲突</button>
                        <button class="btn btn-success ms-2" onclick="testRagFormData()">测试RAG表单数据收集</button>
                        <div id="test-results" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked@5.1.1/marked.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="js/app.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pages.js"></script>
    
    <script>
        function testIdConflict() {
            const results = document.getElementById('test-results');
            
            // 测试原始ID（应该获取大纲生成页面的值）
            const courseId = document.getElementById('course-id').value;
            const courseMaterialId = document.getElementById('course-material-id').value;
            
            // 测试新的RAG专用ID
            const ragCourseId = document.getElementById('rag-course-id').value;
            const ragCourseMaterialId = document.getElementById('rag-course-material-id').value;
            const ragCollectionName = document.getElementById('rag-collection-name').value;
            
            results.innerHTML = `
                <div class="alert alert-success">
                    <h6>ID冲突测试结果：</h6>
                    <ul class="mb-0">
                        <li><strong>course-id:</strong> ${courseId} (应该是 "outline_course")</li>
                        <li><strong>course-material-id:</strong> ${courseMaterialId} (应该是 "outline_material")</li>
                        <li><strong>rag-course-id:</strong> ${ragCourseId} (应该是 "rag_course")</li>
                        <li><strong>rag-course-material-id:</strong> ${ragCourseMaterialId} (应该是 "rag_material")</li>
                        <li><strong>rag-collection-name:</strong> ${ragCollectionName} (应该是 "rag_collection")</li>
                    </ul>
                </div>
            `;
        }
        
        function testRagFormData() {
            const results = document.getElementById('test-results');
            
            // 模拟RAG聊天页面的表单数据收集
            const formData = {
                conversation_id: "test_conversation",
                question: "测试问题",
                chat_engine_type: "condense_plus_context",
                course_id: document.getElementById("rag-course-id").value.trim(),
                course_material_id: document.getElementById("rag-course-material-id").value.trim(),
                collection_name: document.getElementById("rag-collection-name").value.trim()
            };
            
            // 使用ChatAPI.buildChatRequest构建请求数据
            const chatData = ChatAPI.buildChatRequest(formData);
            
            results.innerHTML = `
                <div class="alert alert-info">
                    <h6>RAG表单数据收集测试：</h6>
                    <p><strong>收集的表单数据：</strong></p>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(formData, null, 2)}</pre>
                    <p><strong>构建的聊天数据：</strong></p>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(chatData, null, 2)}</pre>
                    <div class="mt-2">
                        ${chatData.course_material_id ? 
                            '<span class="badge bg-success">✅ course_material_id 存在</span>' : 
                            '<span class="badge bg-danger">❌ course_material_id 不存在</span>'
                        }
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
