# OpenAI API 配置
API_KEY=sk-5LLuQ7Zk9SdvMbKAigi2fZFO27jdCZRmrxxPX85jX11sFywg
BASE_URL=https://api.openai-proxy.org
OUTLINE_MODEL=gpt-4o-mini
REFINE_MODEL=gpt-4o-mini

# RAG 配置 (为后续模块预留)
RAG_EMBED_MODEL=text-embedding-3-small
RAG_LLM_MODEL=gpt-4o-mini
QDRANT_URL=http://localhost:6333

# GraphRAG 配置 (为后续模块预留)
GRAPH_RAG_WORKDIR=./data/outputs/graphrag

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true

# 文件配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=.md,.txt

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
