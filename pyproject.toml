[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-backend"
version = "0.1.0"
description = "AI功能后端 - 文本生成大纲、RAG问答、GraphRAG知识图谱"
authors = [
    {name = "AI Backend Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "fastapi==0.116.1",
    "uvicorn[standard]==0.30.6",
    "pydantic==2.8.2",
    "pydantic-settings==2.4.0",
    "aiofiles==24.1.0",
    "openai==1.99.9",
    "loguru==0.7.2",
    "python-dotenv==1.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest==8.3.2",
    "pytest-asyncio==0.24.0",
    "pytest-mock==3.14.0",
    "httpx==0.27.2",
]
rag = [
    "llama-index==0.13.0",
    "qdrant-client==1.15.1",
]
graphrag = [
    "graphrag==2.4.0",
    "pandas==2.2.2",
    "pyarrow==17.0.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
