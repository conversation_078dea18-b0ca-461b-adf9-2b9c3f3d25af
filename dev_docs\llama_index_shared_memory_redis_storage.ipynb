{"cells": [{"cell_type": "code", "execution_count": 3, "id": "25c02e66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已设置 LLM 和 Embedding。\n", "\n"]}], "source": ["# =============================\n", "# 1) 基础导入与全局设置\n", "# =============================\n", "import os\n", "from llama_index.core import Settings, VectorStoreIndex, PromptTemplate\n", "from llama_index.core import SimpleDirectoryReader\n", "from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.core.chat_engine import SimpleChatEngine\n", "\n", "# 替换成你自己的 OpenAI API Key\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n", "# 如需自定义网关（如代理或 Azure），取消下面注释并替换\n", "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n", "\n", "\n", "\n", "# ➊ 配置全局设置（替代 ServiceContext）\n", "Settings.llm = OpenAI(\n", "    model=\"gpt-4o-mini\", \n", "    temperature=0.1,\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "Settings.embed_model = OpenAIEmbedding(\n", "    model=\"text-embedding-3-small\",\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "\n", "\n", "print(\"✅ 已设置 LLM 和 Embedding。\\n\")"]}, {"cell_type": "code", "execution_count": 4, "id": "08f3f865", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已从Qdrant 6334端口加载向量数据到index\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_44940\\4269384171.py:9: UserWarning: Failed to obtain server version. Unable to check client-server compatibility. Set check_compatibility=False to skip version check.\n", "  qdrant_client = QdrantClient(\n"]}], "source": ["# 连接qdrant\n", "\n", "# 从本地Qdrant 6334端口加载已有向量数据到index\n", "from qdrant_client import QdrantClient\n", "from llama_index.vector_stores.qdrant import QdrantVectorStore\n", "from llama_index.core import StorageContext, VectorStoreIndex\n", "\n", "# 连接到本地Qdrant gRPC端口6334\n", "qdrant_client = QdrantClient(\n", "    host=\"localhost\",\n", "    port=6334,  # gRPC端口，比6333 HTTP端口性能更好\n", "    prefer_grpc=True,\n", "    timeout=10\n", ")\n", "\n", "\n", "# 从已有集合创建向量存储\n", "# 这个qdrant的warning总比崩溃好\n", "collection_name = \"course_materials\"  # 使用已存在的集合\n", "vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "\n", "# 从Qdrant向量存储创建index（不重新写入数据）\n", "index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\n", "\n", "print(\"✅ 已从Qdrant 6334端口加载向量数据到index\")"]}, {"cell_type": "code", "execution_count": 179, "id": "1df2c78e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n", "\n"]}], "source": ["# =============================\n", "#  共享的 ChatSummaryMemoryBuffer, 持久化在redis里面，还没有持久在硬盘里面，别着急\n", "# =============================\n", "from llama_index.storage.chat_store.redis import RedisChatStore\n", "from llama_index.core.memory import ChatSummaryMemoryBuffer\n", "\n", "\n", "chat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\n", "\n", "#ttl是所有存入chat_store的存活时间，单位是秒。注意，chat_store不能随便建立，这个耗时比较长\n", "\n", "custom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n", "\"\"\"\n", "\n", "memory = ChatSummaryMemoryBuffer.from_defaults(\n", "    token_limit = 4000, # 测试用1000，真实用4000，后续估计还有调整，感觉根本用不完\n", "    llm=Settings.llm,  # 用同一个 LLM 进行摘要\n", "    chat_store=chat_store,\n", "    chat_store_key=\"condense_plus_chat\",\n", "    summarize_prompt=custom_summary_prompt\n", ")\n", "\n", "print(memory.summarize_prompt)"]}, {"cell_type": "code", "execution_count": 180, "id": "42a2d357", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\n", "\n"]}], "source": ["# 两种聊天模式加上对应的提示词\n", "\n", "\n", "from llama_index.core.prompts import PromptTemplate\n", "\n", "# \"condense_question\"用的提示词\n", "new_condense_prompt = PromptTemplate(\n", "    \"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\\n\"\n", "    \"注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n\"\n", "    \"=== 聊天历史 ===\\n\"\n", "    \"{chat_history}\\n\\n\"\n", "    \"=== 学生最新提出的问题 ===\\n\"\n", "    \"{question}\\n\\n\"\n", "    \"=== 改写后的独立问题 ===\\n\"\n", ")\n", "\n", "\n", "\n", "# 3. 自定义 context_prompt（整合检索内容和用户问题的核心提示词）\n", "custom_context_prompt = (\n", "    \"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\\n\\n\"\n", "    \"📚 **相关文档内容：**\\n\"\n", "    \"{context_str}\\n\\n\"\n", "    \"🎯 **回答要求：**\\n\"\n", "    \"1. 严格基于上述文档内容进行回答\\n\"\n", "    \"2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\\n\"\n", "    \"3. 回答要条理清晰，使用适当的emoji让内容更生动\\n\"\n", "    \"4. 请引用具体的文档内容来支撑你的回答\\n\\n\"\n", "    \"💡 **请基于以上文档和之前的对话历史来回答用户的问题。**\"\n", "     \"根据以上信息，请回答这个问题: {query_str}\\n\\n\" #这里放一个{query_str}可以但是可能不合适，不过我觉得是最佳实践 ---by James\n", "     \"====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\\n\\n\"\n", ")\n", "\n", "# 4. 创建 condense_plus_context 引擎（正确的配置方式）\n", "\n", "\n", "condense_question_plus_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_plus_context\",\n", "    condense_prompt=new_condense_prompt,\n", "    context_prompt=custom_context_prompt,             # 配置上下文整合提示词\n", "    memory=memory,\n", "    # system_prompt=\"你是文文，一个热心活泼乐于助人的ai聊天助手，擅长查资料。你总是简洁、清晰、有条理地回应，使用很多的emoji。你回答总用“哈哈”开头。\",\n", "    verbose=True,\n", ")\n", "\n", "# 在condense_question_plus模式下，给llm的内容是context_prompt_template + system_prompt + chat_history + query_str,所以其实system_prompt没有必要\n", "\n", "# simple 引擎（不检索，只与LLM聊天，但同样共享 memory\n", "simple_engine = SimpleChatEngine.from_defaults(\n", "    llm=Settings.llm,\n", "    memory=memory,\n", "    system_prompt=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\",\n", "    verbose=True\n", ")\n", "print(\"✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "e41eb37b", "metadata": {}, "outputs": [], "source": ["# 设置动态过滤样例代码\n", "\n", "# 动态过滤查询函数\n", "def filtered_query(question, course_id=None, material_id=None):\n", "    filters_list = []\n", "    \n", "    if course_id:\n", "        filters_list.append(\n", "            MetadataFilter(key=\"course_id\", value=course_id, operator=FilterOperator.EQ)\n", "        )\n", "    \n", "    if material_id:\n", "        filters_list.append(\n", "            MetadataFilter(key=\"course_material_id\", value=material_id, operator=FilterOperator.EQ)\n", "        )\n", "    \n", "    if filters_list:\n", "        filters = MetadataFilters(filters=filters_list)\n", "        query_engine = index.as_query_engine(similarity_top_k=3, filters=filters)\n", "    else:\n", "        query_engine = index.as_query_engine(similarity_top_k=3)\n", "    \n", "    return query_engine.query(question)\n", "\n", "# 使用示例\n", "print(\"=== 不同过滤模式测试 ===\")\n", "print(\"1. 无过滤:\")\n", "result1 = filtered_query(\"函数的核心概念\")\n", "print(f\"结果: {result1}\\n\")\n", "\n", "print(\"2. 按course_id过滤:\")\n", "result2 = filtered_query(\"函数的核心概念\", course_id=\"course_01\")\n", "print(f\"结果: {result2}\\n\")\n", "\n", "print(\"3. 按material_id过滤:\")\n", "result3 = filtered_query(\"函数的核心概念\", material_id=\"material_001\")\n", "print(f\"结果: {result3}\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "47d7ae05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "🤖 智能聊天助手 - 请输入以下信息：\n", "============================================================\n", "\n", "🔍 使用引擎类型: condense_plus_context\n", "📄 过滤条件: course_material_id = material_红楼梦第一章\n", "💬 对话ID: try002\n", "\n", "--------------------------------------------------\n", "🤖 正在思考中...\n", "--------------------------------------------------\n", "Condensed question: 在《红楼梦》中，王夫人的娘家是哪个家族？请详细介绍一下王夫人与她娘家的关系，以及他们在故事中的重要性。\n", "\n", "🤖 文文的回答：\n", "==================================================\n", "王夫人的娘家是王家。在《红楼梦》中，王夫人是贾母的女婿，王熙凤的母亲，属于贾府的外戚。王家在小说中与贾、史、薛三大家族有着密切的联系。\n", "\n", "如果你还有其他问题或想了解更多内容，随时告诉我哦！😊✨\n", "==================================================\n", "\n", "📚 检索到的匹配文本块 (共 2 个)：\n", "============================================================\n", "\n", "📄 文本块 1:\n", "   相似度分数: 0.6160187721252441\n", "   元数据: {'course_material_id': 'material_红楼梦第一章', 'course_id': 'course_01', 'chunk_index': 5, 'file_path': '红楼梦第一章.md', 'course_material_name': '红楼梦第一章'}\n", "   内容预览: ### 2.3 读书笔记与思维导图建议  《红楼梦》适合做精细的读书笔记和结构化梳理。建议使用以下方法：  1. **人物关系图/思维导图**：用XMind、幕布、Markmap等工具梳理主要人物关系与故事线。 2. **章节摘要**：每读一回，写简要摘要和主要事件。 3. **主题归纳**：按爱情、家族、女性、宗教等主题归纳相关内容。 4. **诗词赏析**：摘录并分析重要诗词、判词和谶语。 5...\n", "------------------------------------------------------------\n", "\n", "📄 文本块 2:\n", "   相似度分数: 0.6045393347740173\n", "   元数据: {'course_material_id': 'material0002', 'course_id': 'course0002', 'file_path': None, 'course_material_name': '红楼梦第一章', 'chunk_index': 5}\n", "   内容预览: 3 读书笔记与思维导图建议  《红楼梦》适合做精细的读书笔记和结构化梳理。建议使用以下方法：  1. **人物关系图/思维导图**：用XMind、幕布、Markmap等工具梳理主要人物关系与故事线。 2. **章节摘要**：每读一回，写简要摘要和主要事件。 3. **主题归纳**：按爱情、家族、女性、宗教等主题归纳相关内容。 4. **诗词赏析**：摘录并分析重要诗词、判词和谶语。 5. **个人...\n", "------------------------------------------------------------\n", "\n", "============================================================\n", "🤖 智能聊天助手 - 请输入以下信息：\n", "============================================================\n", "\n", "🔍 使用引擎类型: simple\n", "📄 过滤条件: course_material_id = material_红楼梦第一章\n", "💬 对话ID: try002\n", "\n", "--------------------------------------------------\n", "🤖 正在思考中...\n", "--------------------------------------------------\n", "\n", "🤖 文文的回答：\n", "==================================================\n", "当然可以！让我们来回顾一下我们刚才的讨论内容！📚✨\n", "\n", "### 讨论内容总结\n", "\n", "1. **关于《红楼梦》中能工巧匠的介绍**：\n", "   - 我们提到了《红楼梦》中一些重要角色，如王熙凤、贾宝玉、林黛玉和贾母，虽然没有明确的“能工巧匠”，但他们在各自领域展现了非凡的才能和智慧。\n", "\n", "2. **林黛玉和薛宝钗的悲剧**：\n", "   - 我们讨论了暗示林黛玉和薛宝钗悲剧命运的一句话，提到“木石前盟”与“金玉良缘”的象征，反映了理想爱情与现实婚姻之间的冲突。\n", "\n", "3. **王夫人的娘家**：\n", "   - 我们确认了王夫人的娘家是王家，王夫人是贾母的女婿，王熙凤的母亲，属于贾府的外戚。\n", "\n", "### 总结\n", "\n", "我们的讨论涵盖了《红楼梦》中人物的背景、情感冲突以及家族关系等多个方面。如果你还有其他问题或想深入探讨的内容，随时告诉我哦！😊🎉\n", "==================================================\n", "\n", "⚠️  注意：此次回答未检索到相关文档片段\n", "   (simple 模式不进行文档检索，这是正常的)\n", "\n", "============================================================\n", "🤖 智能聊天助手 - 请输入以下信息：\n", "============================================================\n", "👋 再见！\n"]}], "source": ["# 🎯 用户交互式聊天系统 - 支持动态过滤和多种聊天引擎\n", "# 基于现有的 llama_index_shared_memory_redis_storage copy.ipynb\n", "\n", "# 📦 导入必要的过滤器模块\n", "from llama_index.core.vector_stores import MetadataFilter, MetadataFilters, FilterOperator\n", "from llama_index.storage.chat_store.redis import RedisChatStore\n", "from llama_index.core.memory import ChatSummaryMemoryBuffer\n", "\n", "# 🔄 主循环：用户交互式问答\n", "while True:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🤖 智能聊天助手 - 请输入以下信息：\")\n", "    print(\"=\"*60)\n", "    \n", "    # 📝 获取用户输入\n", "    conversation_id = input(\"💬 请输入 conversation_id: \").strip()\n", "    course_id = input(\"📚 请输入 course_id (可选，留空则不过滤): \").strip()\n", "    course_material_id = input(\"📄 请输入 course_material_id (可选，留空则不过滤): \").strip()\n", "    chat_engine_type = input(\"🔧 请输入 chat_engine_type (condense_plus_context/simple): \").strip()\n", "    user_question = input(\"❓ 请输入您的问题: \").strip()\n", "    \n", "    # 🛑 退出条件\n", "    if user_question.lower() in ['quit', 'exit', '退出']:\n", "        print(\"👋 再见！\")\n", "        break\n", "    \n", "    # 🔍 创建基于用户输入的 memory 和 chat_store\n", "    chat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\n", "    \n", "    custom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n", "    \"\"\"\n", "    \n", "    # 🧠 使用用户输入的 conversation_id 作为 chat_store_key\n", "    memory = ChatSummaryMemoryBuffer.from_defaults(\n", "        token_limit=4000,\n", "        llm=Settings.llm,\n", "        chat_store=chat_store,\n", "        chat_store_key=conversation_id,  # 🔑 使用用户输入的 conversation_id\n", "        summarize_prompt=custom_summary_prompt\n", "    )\n", "    \n", "    # 🎛️ 根据用户输入创建对应的 chat_engine\n", "    if chat_engine_type == \"condense_plus_context\":\n", "        # 📋 使用现有的 condense_question_plus_engine 配置\n", "        new_condense_prompt = PromptTemplate(\n", "            \"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\\n\"\n", "            \"注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n\"\n", "            \"=== 聊天历史 ===\\n\"\n", "            \"{chat_history}\\n\\n\"\n", "            \"=== 学生最新提出的问题 ===\\n\"\n", "            \"{question}\\n\\n\"\n", "            \"=== 改写后的独立问题 ===\\n\"\n", "        )\n", "        \n", "        custom_context_prompt = (\n", "            \"你叫做文文，一个严谨专业热情的ai聊天助手，擅长查找资料，你总是谨慎判断资料和用户的问题是否相关。而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\\n\\n\"\n", "            \"📚 **相关文档内容：**\\n\"\n", "            \"{context_str}\\n\\n\"\n", "            \"🎯 **回答要求：**\\n\"\n", "            \"1. 严格基于上述文档内容进行回答\\n\"\n", "            \"2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\\n\"\n", "            \"3. 回答要条理清晰，使用适当的emoji让内容更生动\\n\"\n", "            \"4. 请引用具体的文档内容来支撑你的回答\\n\\n\"\n", "            \"💡 **请基于以上文档和之前的对话历史来回答用户的问题。**\"\n", "            \"根据以上信息，请回答这个问题: {query_str}\\n\\n\"\n", "            \"====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\\n\\n\"\n", "        )\n", "        \n", "        # 🔧 创建带过滤器的 condense_plus_context 引擎\n", "        filters_list = []\n", "        \n", "        # 🎯 根据用户输入设置过滤器 - course_id 和 course_material_id 只能存在一个\n", "        if course_id and course_material_id:\n", "            print(\"⚠️  警告：course_id 和 course_material_id 只能选择一个，优先使用 course_id\")\n", "            filters_list.append(\n", "                MetadataFilter(key=\"course_id\", value=course_id, operator=FilterOperator.EQ)\n", "            )\n", "        elif course_id:\n", "            filters_list.append(\n", "                MetadataFilter(key=\"course_id\", value=course_id, operator=FilterOperator.EQ)\n", "            )\n", "        elif course_material_id:\n", "            filters_list.append(\n", "                MetadataFilter(key=\"course_material_id\", value=course_material_id, operator=FilterOperator.EQ)\n", "            )\n", "        else:\n", "            continue;\n", "        \n", "        # 🔍 创建带过滤器的查询引擎\n", "        if filters_list:\n", "            filters = MetadataFilters(filters=filters_list)\n", "            query_engine = index.as_query_engine(similarity_top_k=6, filters=filters)\n", "        else:\n", "            query_engine = index.as_query_engine(similarity_top_k=6)\n", "        \n", "        # 🚀 创建 condense_plus_context 聊天引擎\n", "        chat_engine = index.as_chat_engine(\n", "            chat_mode=\"condense_plus_context\",\n", "            condense_prompt=new_condense_prompt,\n", "            context_prompt=custom_context_prompt,\n", "            memory=memory,\n", "            verbose=True,\n", "        )\n", "        \n", "        # 🔧 手动设置过滤器到聊天引擎的查询引擎\n", "        if filters_list:\n", "            chat_engine._query_engine = query_engine\n", "        \n", "    elif chat_engine_type == \"simple\":\n", "        # 🎭 使用现有的 simple_engine 配置\n", "        chat_engine = SimpleChatEngine.from_defaults(\n", "            llm=Settings.llm,\n", "            memory=memory,\n", "            system_prompt=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\",\n", "            verbose=True\n", "        )\n", "    else:\n", "        print(\"❌ 错误：chat_engine_type 只支持 'condense_plus_context' 或 'simple'\")\n", "        continue\n", "    \n", "    # 💬 执行对话\n", "    print(f\"\\n🔍 使用引擎类型: {chat_engine_type}\")\n", "    if course_id:\n", "        print(f\"📚 过滤条件: course_id = {course_id}\")\n", "    elif course_material_id:\n", "        print(f\"📄 过滤条件: course_material_id = {course_material_id}\")\n", "    else:\n", "        print(\"🌐 无过滤条件，搜索全部文档\")\n", "    \n", "    print(f\"💬 对话ID: {conversation_id}\")\n", "    print(\"\\n\" + \"-\"*50)\n", "    print(\"🤖 正在思考中...\")\n", "    print(\"-\"*50)\n", "    \n", "\n", "    # 🎯 获取回答\n", "    response = chat_engine.chat(user_question)\n", "\n", "    print(f\"\\n🤖 文文的回答：\")\n", "    print(\"=\"*50)\n", "    print(response)\n", "    print(\"=\"*50)\n", "\n", "    # 📚 显示匹配的文本块\n", "    if hasattr(response, 'source_nodes') and response.source_nodes:\n", "        print(f\"\\n📚 检索到的匹配文本块 (共 {len(response.source_nodes)} 个)：\")\n", "        print(\"=\"*60)\n", "        \n", "        for i, source_node in enumerate(response.source_nodes, 1):\n", "            content = source_node.node.get_content().strip().replace(\"\\n\", \" \")\n", "            \n", "            if len(content) > 200:\n", "                content_preview = content[:200] + \"...\"\n", "            else:\n", "                content_preview = content\n", "                \n", "            score = getattr(source_node, 'score', 'N/A')\n", "            \n", "            print(f\"\\n📄 文本块 {i}:\")\n", "            print(f\"   相似度分数: {score}\")\n", "            print(f\"   元数据: {source_node.node.metadata}\")\n", "            print(f\"   内容预览: {content_preview}\")\n", "            print(\"-\" * 60)\n", "    else:\n", "        print(f\"\\n⚠️  注意：此次回答未检索到相关文档片段\")\n", "        if chat_engine_type == \"simple\":\n", "            print(\"   (simple 模式不进行文档检索，这是正常的)\")\n", "        \n", "    "]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}